# frozen_string_literal: true

t1 = Tenant.create!(
  name: 'sole', domain: 'sole.smart-energy.localhost'
)
Tenant.create!(
  name: 'mare', domain: 'mare.smart-energy.localhost'
)

p1 = Person.new(
  first_name: '<PERSON><PERSON><PERSON><PERSON>', last_name: '<PERSON><PERSON><PERSON><PERSON>'
)
p2 = Person.new(
  first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
)

c1 = Client.new(
  tax_code: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', phone_number: '<PERSON>ian PHONE NUMBER', email: '<EMAIL>'
)
c1.tenant = t1
c1.clientable = p1

c1.save!

c2 = Client.new(
  tax_code: 'Daniel<PERSON>XCODE', phone_number: '<PERSON> PHONE NUMBER', email: '<EMAIL>'
)
c2.tenant = t1
c2.clientable = p2

c2.save!

co1 = Company.new(
  company_name: 'COMPANY 1', registration_number: 'REGISTRATION NUMBER 1'
)

c3 = Client.new(
  tax_code: 'COMPANY1TAXCODE', phone_number: 'COMPANY 1 PHONE NUMBER', email: '<EMAIL>'
)
c3.tenant = t1
c3.clientable = co1

c3.save!

co2 = Company.new(
  company_name: 'COMPANY 2', registration_number: 'REGISTRATION NUMBER 2'
)

c4 = Client.new(
  tax_code: 'COMPANY2TAXCODE', phone_number: 'COMPANY 2 PHONE NUMBER', email: '<EMAIL>'
)
c4.tenant = t1
c4.clientable = co2

c4.save!
