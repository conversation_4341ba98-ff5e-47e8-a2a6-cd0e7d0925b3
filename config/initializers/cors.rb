# frozen_string_literal: true

# Be sure to restart your server when you modify this file.

# Avoid CORS issues when API is called from the frontend app.
# Handle Cross-Origin Resource Sharing (CORS) in order to accept cross-origin Ajax requests.

# Read more: https://github.com/cyu/rack-cors

Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    origins do |source|
      Cors::AllowedOriginValidator.call(source)
    rescue StandardError => e
      Rails.logger.error("CORS Check: Error during origin validation for Origin '#{source}'. Error: #{e.class} - #{e.message}")

      # In case of unexpected errors, deny access for safety.
      false
    end

    resource '*',
             headers: :any,
             methods: %i[get post put patch delete options head],
             credentials: true # Keep this as you need credentials
  end
end
