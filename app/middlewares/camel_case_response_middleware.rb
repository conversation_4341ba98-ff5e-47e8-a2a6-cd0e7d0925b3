# frozen_string_literal: true

# Middleware to transform JSON response keys from snake_case to camelCase
# Uses the centralized CaseTransformer service for consistent transformation
class CamelCaseResponseMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    status, headers, response = @app.call(env)

    if json_response?(headers)
      begin
        new_response = transform_response(response)
        # Update Content-Length header if it exists
        headers['Content-Length'] = new_response.sum(&:bytesize).to_s if headers['Content-Length']
        [status, headers, new_response]
      rescue StandardError => e
        Rails.logger.error("Error transforming response to camelCase: #{e.message}")
        [status, headers, response] # Return original response on error
      end
    else
      [status, headers, response]
    end
  end

  private

  def json_response?(headers)
    headers['Content-Type']&.include?('application/json')
  end

  def transform_response(response)
    transformed_response = []

    response.each do |body|
      next if body.empty?

      begin
        # Parse JSON and transform to camelCase using the centralized service
        json = Oj.load(body, mode: :compat)
        transformed_body = CaseTransformer.to_camel_case_json(json)
        transformed_response << transformed_body
      rescue Oj::ParseError, EncodingError => e
        Rails.logger.error("JSON parse error in response: #{e.message}")
        transformed_response << body # Keep original body on parse error
      end
    end

    transformed_response
  end
end
