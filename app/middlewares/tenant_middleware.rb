# frozen_string_literal: true

require 'uri'

class TenantMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    request = ActionDispatch::Request.new(env)

    origin = request.headers['Origin']
    # Parse the host from the Origin header if it exists
    domain = origin ? URI.parse(origin).host : nil

    tenant = Tenant.find_by(domain: domain)

    # Consider what to do if the tenant is not found based on Origin/Host
    raise ActionController::RoutingError, 'Invalid domain or origin' unless tenant

    Current.tenant = tenant

    @app.call(env)
  end
end
