# frozen_string_literal: true

# == Schema Information
#
# Table name: clients
#
#  id              :bigint           not null, primary key
#  clientable_type :string           not null
#  email           :string
#  phone_number    :string
#  tax_code        :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  clientable_id   :bigint           not null
#  tenant_id       :bigint           not null
#
# Indexes
#
#  index_clients_on_clientable  (clientable_type,clientable_id)
#  index_clients_on_tenant_id   (tenant_id)
#
# Foreign Keys
#
#  fk_rails_...  (tenant_id => tenants.id)
#
class Client < ApplicationRecord
  include Tenantable

  delegated_type :clientable, types: %w[Person Company], inverse_of: :client
  accepts_nested_attributes_for :clientable

  validates :clientable_type, inclusion: { in: %w[Person Company] }

  def person?
    clientable_type == 'Person'
  end

  def company?
    clientable_type == 'Company'
  end
end
