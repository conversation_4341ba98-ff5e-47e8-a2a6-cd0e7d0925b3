# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Resources
        class Companies < Grape::API
          resource :companies do
            desc 'Get all companies'
            get do
              companies = Company.all
              present companies, with: SmartEnergy::Api::V1::Entities::CompanyEntity
            end

            desc 'Get a specific company'
            params do
              requires :id, type: Integer, desc: 'Company ID'
            end
            get ':id' do
              company = Company.find(params[:id])
              present company, with: SmartEnergy::Api::V1::Entities::CompanyEntity
            end

            desc 'Create a new company'
            params do
              requires :company_name, type: String, desc: 'Company name'
              optional :registration_number, type: String, desc: 'Registration number'
              optional :client, type: Hash do
                optional :tax_code, type: String, desc: 'Tax code'
                optional :phone_number, type: String, desc: 'Phone number'
                optional :email, type: String, desc: 'Email'
              end
            end
            post do
              company = Company.new(permitted_params(%i[company_name registration_number]))

              ActiveRecord::Base.transaction do
                if company.save
                  # Client will be automatically created by the after_create callback
                  # Update client with provided parameters if any
                  if params[:client].present?
                    client_params = permitted_params([:client])[:client]
                    company.client.update!(client_params) if client_params.present?
                  end

                  present company, with: SmartEnergy::Api::V1::Entities::CompanyEntity
                else
                  error!({ errors: company.errors.full_messages }, 422)
                end
              end
            end

            desc 'Update a company'
            params do
              requires :id, type: Integer, desc: 'Company ID'
              optional :company_name, type: String, desc: 'Company name'
              optional :registration_number, type: String, desc: 'Registration number'
            end
            put ':id' do
              company = Company.find(params[:id])

              if company.update(permitted_params(%i[company_name registration_number]))
                present company, with: SmartEnergy::Api::V1::Entities::CompanyEntity
              else
                error!({ errors: company.errors.full_messages }, 422)
              end
            end

            desc 'Delete a company'
            params do
              requires :id, type: Integer, desc: 'Company ID'
            end
            delete ':id' do
              company = Company.find(params[:id])
              company.destroy
              status 204
            end
          end
        end
      end
    end
  end
end
