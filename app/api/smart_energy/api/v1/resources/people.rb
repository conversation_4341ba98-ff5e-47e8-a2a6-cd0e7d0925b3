# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Resources
        class People < Grape::API
          resource :people do
            desc 'Get all people'
            get do
              people = Person.all
              present people, with: SmartEnergy::Api::V1::Entities::PersonEntity
            end

            desc 'Get a specific person'
            params do
              requires :id, type: Integer, desc: 'Person ID'
            end
            get ':id' do
              person = Person.find(params[:id])
              present person, with: SmartEnergy::Api::V1::Entities::PersonEntity
            end

            desc 'Create a new person'
            params do
              requires :first_name, type: String, desc: 'First name'
              requires :last_name, type: String, desc: 'Last name'
              requires :client, type: Hash do
                requires :tax_code, type: String, desc: 'Tax code'
                requires :phone_number, type: String, desc: 'Phone number'
                requires :email, type: String, desc: 'Email'
              end
            end
            post do
              person = Person.new(permitted_params(%i[first_name last_name]))

              ActiveRecord::Base.transaction do
                if person.save
                  # Client will be automatically created by the after_create callback
                  # Update client with provided parameters if any
                  if params[:client].present?
                    client_params = permitted_params([:client])[:client]
                    person.client.update!(client_params) if client_params.present?
                  end

                  present person, with: SmartEnergy::Api::V1::Entities::PersonEntity
                else
                  error!({ errors: person.errors.full_messages }, 422)
                end
              end
            end

            desc 'Update a person'
            params do
              requires :id, type: Integer, desc: 'Person ID'
              optional :first_name, type: String, desc: 'First name'
              optional :last_name, type: String, desc: 'Last name'
            end
            put ':id' do
              person = Person.find(params[:id])

              if person.update(permitted_params(%i[first_name last_name]))
                present person, with: SmartEnergy::Api::V1::Entities::PersonEntity
              else
                error!({ errors: person.errors.full_messages }, 422)
              end
            end

            desc 'Delete a person'
            params do
              requires :id, type: Integer, desc: 'Person ID'
            end
            delete ':id' do
              person = Person.find(params[:id])
              person.destroy
              status 204
            end
          end
        end
      end
    end
  end
end
