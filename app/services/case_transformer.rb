# frozen_string_literal: true

require 'oj'
require 'active_support/core_ext/string/inflections' # For underscore and camelize
require 'active_support/core_ext/hash/keys' # For deep_symbolize_keys
require 'active_support/core_ext/hash/indifferent_access' # For with_indifferent_access
require 'active_support/core_ext/object/blank' # For blank?

# CaseTransformer service handles conversion between camelCase and snake_case
# This centralized service ensures consistent transformation across the application
class CaseTransformer
  class << self
    # Transform a hash or array from camelCase to snake_case
    # @param data [Hash, Array, Object] The data to transform
    # @param symbolize [Boolean] Whether to symbolize keys (default: true)
    # @return [Hash, Array, Object] The transformed data with symbol keys or with indifferent access
    # @example
    #   CaseTransformer.camel_to_snake({firstName: 'John'}) # => {:first_name=>'John'}
    def camel_to_snake(data, symbolize: true)
      return data if data.nil?

      result = deep_transform_keys(data) { |key| key.to_s.underscore }

      if result.is_a?(Hash)
        symbolize ? result.deep_symbolize_keys : result.with_indifferent_access
      else
        result
      end
    end

    # Transform a hash or array from snake_case to camelCase
    # @param data [Hash, Array, Object] The data to transform
    # @param stringify [Boolean] Whether to stringify keys (default: true)
    # @return [Hash, Array, Object] The transformed data
    # @example
    #   CaseTransformer.snake_to_camel({first_name: 'John'}) # => {"firstName"=>"John"}
    def snake_to_camel(data, stringify: true)
      return data if data.nil?

      result = deep_transform_keys(data) { |key| key.to_s.camelize(:lower) }

      if result.is_a?(Hash) && stringify
        result.transform_keys(&:to_s)
      else
        result
      end
    end

    # Parse JSON string and transform keys from camelCase to snake_case
    # @param json_string [String] The JSON string to parse and transform
    # @return [Hash, Array] The parsed and transformed data with indifferent access for hashes
    # @raise [Oj::ParseError, EncodingError] If the JSON is invalid
    # @example
    #   CaseTransformer.parse_json_to_snake_case('{"firstName":"John"}') # => {"first_name"=>"John"}
    def parse_json_to_snake_case(json_string)
      return {} if json_string.blank?

      data = Oj.load(json_string, mode: :compat)
      # We return arrays directly instead of wrapping them in a _json key
      # This is more intuitive and consistent with how JSON is typically handled
      camel_to_snake(data, symbolize: false)
    rescue Oj::ParseError, EncodingError => e
      Rails.logger.error("JSON parse error: #{e.message}")
      {}
    end

    # Transform data to camelCase and convert to JSON string
    # @param data [Hash, Array, Object] The data to transform and convert
    # @return [String] The JSON string with camelCase keys
    # @example
    #   CaseTransformer.to_camel_case_json({first_name: 'John'}) # => '{"firstName":"John"}'
    def to_camel_case_json(data)
      return '{}' if data.nil?

      transformed_data = snake_to_camel(data)
      Oj.dump(transformed_data, mode: :compat)
    rescue Oj::Error, NoMethodError, TypeError => e
      Rails.logger.error("Error converting to camelCase JSON: #{e.message}")
      Oj.dump(data, mode: :compat) # Return original data as JSON if transformation fails
    end

    private

    # Recursively transform keys in a hash or array
    # @param object [Hash, Array, Object] The object to transform
    # @param block [Proc] The transformation to apply to each key
    # @return [Hash, Array, Object] The transformed object
    # @example
    #   deep_transform_keys({key: 'value'}) { |k| k.to_s.upcase } # => {"KEY"=>"value"}
    def deep_transform_keys(object, &block)
      return nil if object.nil?

      case object
      when Hash
        object.each_with_object({}) do |(key, value), result|
          result[yield(key)] = deep_transform_keys(value, &block)
        end
      when Array
        object.map { |item| deep_transform_keys(item, &block) }
      else
        object
      end
    end
  end
end
