# frozen_string_literal: true

module Cors
  # Validates if a given CORS Origin header source corresponds to a currently allowed origin
  class AllowedOriginValidator
    def self.call(source)
      return false unless source

      begin
        uri = URI.parse(source)

        Tenant.exists?(domain: uri.host)
      rescue URI::InvalidURIError => e
        Rails.logger.warn("CORS AllowedOriginValidator: Invalid Origin URI received: #{source}. Error: #{e.message}")
        false # Deny access if Origin format is invalid
        # Note: We intentionally DO NOT rescue StandardError here.
        # Let database connection errors etc. bubble up to the caller (cors.rb)
        # so it can handle them appropriately (e.g., log and fail closed).
      end
    end
  end
end
