# 4. Main CommodityContract Model - Link Service, Tenant, Client (Polymorphic)
# Note: commodity_contract_service_id column will be created
rails g scaffold CommodityContract tenant:references clientable:references{polymorphic} commodity_type

# 5. Namespaced Service Model (within CommodityContract)
# Use --skip-namespace to avoid overwriting/modifying app/models/commodity_contract.rb
rails g scaffold CommodityContract::Service name --skip-namespace

# 5.1 Add CommodityContract::Service reference to CommodityContract model
rails g migration add_commodity_contract_service_to_commodity_contract commodity_contract_service:references

# 6. Delivery Points
rails g scaffold DeliveryPoint identification_number type street_address address_locality address_region postal_code address_country custom_attributes:json

# 7. Join Tables
rails g scaffold ClientDeliveryPoint tenant:references clientable:references{polymorphic} delivery_point:references usage_type
rails g scaffold CommodityContractDeliveryPoint commodity_contract:references delivery_point:references




rails g scaffold Person tenant:references first_name last_name   
rails g scaffold Company tenant:references company_name  registration_number  
