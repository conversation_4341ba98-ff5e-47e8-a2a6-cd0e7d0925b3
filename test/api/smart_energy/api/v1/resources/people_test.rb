# frozen_string_literal: true

require 'test_helper'

class PeopleTest < ActiveSupport::TestCase
  setup do
    @tenant = Tenant.create!(name: 'Test Tenant', domain: 'test.example.com')
    Current.tenant = @tenant
    @api = SmartEnergy::Api::V1::Resources::People.new
  end

  test "creating a person automatically creates a client" do
    # Arrange
    person_count_before = Person.count
    client_count_before = Client.count

    # Create a person through the model
    person = Person.create!(first_name: '<PERSON>', last_name: '<PERSON><PERSON>')

    # Assert
    assert_equal person_count_before + 1, Person.count, "A person should be created"
    assert_equal client_count_before + 1, Client.count, "A client should be created"
    assert person.client.present?, "Client should be created in database"
    assert_equal @tenant.id, person.client.tenant_id, "Client should belong to current tenant"
  end

  test "creating a person with client attributes" do
    # Arrange & Act
    person = Person.create!(first_name: '<PERSON>', last_name: '<PERSON>')
    person.client.update!(
      tax_code: 'TAX123',
      phone_number: '555-1234',
      email: '<EMAIL>'
    )

    # Assert
    person.reload
    assert_equal 'TAX123', person.client.tax_code
    assert_equal '555-1234', person.client.phone_number
    assert_equal '<EMAIL>', person.client.email
  end

  teardown do
    Current.tenant = nil
  end
end
