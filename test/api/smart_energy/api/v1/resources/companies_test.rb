# frozen_string_literal: true

require 'test_helper'

module SmartEnergy
  module Api
    module V1
      module Resources
        class CompaniesTest < ActionDispatch::IntegrationTest
          setup do
            @tenant = Tenant.create!(name: 'Test Tenant', domain: 'test.example.com')
            Current.tenant = @tenant
          end

          test "creating a company automatically creates a client" do
            # Arrange
            company_params = {
              company_name: 'Acme Inc',
              registration_number: '12345'
            }

            # Act
            post '/api/v1/companies', params: company_params
            
            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            assert_not_nil json_response['client'], "Response should include client information"
            
            # Verify in database
            company = Company.find(json_response['id'])
            assert company.client.present?, "Client should be created in database"
            assert_equal @tenant.id, company.client.tenant_id, "Client should belong to current tenant"
          end

          test "creating a company with client attributes" do
            # Arrange
            company_params = {
              company_name: 'Beta Corp',
              registration_number: '67890',
              client: {
                tax_code: 'CORP123',
                phone_number: '555-6789',
                email: '<EMAIL>'
              }
            }

            # Act
            post '/api/v1/companies', params: company_params
            
            # Assert
            assert_response :success
            json_response = JSON.parse(response.body)
            
            # Verify client attributes in response
            assert_equal 'CORP123', json_response['client']['tax_code']
            assert_equal '555-6789', json_response['client']['phone_number']
            assert_equal '<EMAIL>', json_response['client']['email']
            
            # Verify in database
            company = Company.find(json_response['id'])
            assert_equal 'CORP123', company.client.tax_code
            assert_equal '555-6789', company.client.phone_number
            assert_equal '<EMAIL>', company.client.email
          end

          teardown do
            Current.tenant = nil
          end
        end
      end
    end
  end
end
