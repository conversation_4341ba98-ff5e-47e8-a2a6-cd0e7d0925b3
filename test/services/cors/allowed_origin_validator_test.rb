# frozen_string_literal: true

require 'test_helper'

module Cors
  class AllowedOriginValidatorTest < ActiveSupport::TestCase
    # Test valid origin with existing tenant
    def test_returns_true_for_valid_origin_with_existing_tenant
      # Arrange
      valid_host = 'valid-tenant.example.com'
      origin = "https://#{valid_host}"

      # Stub Tenant.exists? to return true for this domain
      Tenant.stub :exists?, true, [domain: valid_host] do
        # Act
        result = Cors::AllowedOriginValidator.call(origin)

        # Assert
        assert result, "Expected validator to return true for known tenant '#{valid_host}'"
      end
    end

    # Test valid origin with non-existent tenant
    def test_returns_false_for_valid_origin_with_nonexistent_tenant
      # Arrange
      valid_host = 'invalid-tenant.example.com'
      origin = "https://#{valid_host}"

      # Stub Tenant.exists? to return false for this domain
      Tenant.stub :exists?, false, [domain: valid_host] do
        # Act
        result = Cors::AllowedOriginValidator.call(origin)

        # Assert
        assert_not result, "Expected validator to return false for unknown tenant '#{valid_host}'"
      end
    end

    # Test Tenant.exists? raising an error
    def test_propagates_error_when_tenant_exists_raises
      # Arrange
      valid_host = 'error-tenant.example.com'
      origin = "https://#{valid_host}"
      database_error = StandardError.new('DB connection error')

      # Stub Tenant.exists? to raise an error
      Tenant.stub :exists?, ->(*_) { raise database_error } do
        # Act & Assert
        error = assert_raises StandardError, 'Expected database error during Tenant lookup to propagate' do
          Cors::AllowedOriginValidator.call(origin)
        end

        # Verify it's the same error
        assert_equal database_error, error
      end
    end

    # Test URI parsing resulting in nil host
    def test_returns_false_when_uri_host_is_nil
      # Arrange
      origin = 'file:///' # URI that parses with nil host

      # Stub Tenant.exists? for nil domain
      Tenant.stub :exists?, false, [domain: nil] do
        # Act
        result = Cors::AllowedOriginValidator.call(origin)

        # Assert
        assert_not result, 'Expected validator to return false when URI host is nil'
      end
    end

    # Test nil origin
    def test_returns_false_for_nil_origin
      # Arrange
      origin = nil

      # Act
      result = Cors::AllowedOriginValidator.call(origin)

      # Assert
      assert_not result, 'Expected validator to return false for nil origin'
    end

    # Test propagation of non-InvalidURIError StandardError
    def test_propagates_non_invalidurierror_standard_errors
      # Arrange
      origin = 'https://another-valid-host.com'
      parse_error = StandardError.new('Unexpected parsing issue')

      # Act & Assert
      with_uri_parse_raising(origin, parse_error) do
        error = assert_raises StandardError, 'Expected unexpected URI parsing error to propagate' do
          Cors::AllowedOriginValidator.call(origin)
        end
        assert_equal parse_error, error
      end
    end

    # Add test for origin with port
    def test_handles_origin_with_port_correctly
      # Arrange
      valid_host = 'valid-tenant.example.com'
      origin = "https://#{valid_host}:443"

      # Stub Tenant.exists? to return true for this domain (without port)
      Tenant.stub :exists?, true, [domain: valid_host] do
        # Act
        result = Cors::AllowedOriginValidator.call(origin)

        # Assert
        assert result, 'Expected validator to properly extract host without port for existing tenant'
      end
    end

    private

    # Helper to stub URI.parse and ensure restoration
    def with_uri_parse_raising(origin_to_fail, error_to_raise)
      original_parse = URI.method(:parse)
      URI.define_singleton_method(:parse) do |uri_str|
        raise error_to_raise if uri_str == origin_to_fail

        original_parse.call(uri_str)
      end

      yield
    ensure
      # Restore the original parse method
      URI.singleton_class.remove_method(:parse) if URI.singleton_class.method_defined?(:parse)
      URI.define_singleton_method(:parse, original_parse)
    end
  end
end
