# frozen_string_literal: true

require 'test_helper'

class CaseTransformerTest < ActiveSupport::TestCase
  test "camel_to_snake transforms camelCase to snake_case" do
    # Test with a hash
    camel_case_hash = {
      firstName: '<PERSON>',
      lastName: 'Doe',
      emailAddress: '<EMAIL>',
      nestedObject: {
        itemId: 123,
        itemName: 'Test Item'
      },
      arrayOfObjects: [
        { objectId: 1, objectName: 'First' },
        { objectId: 2, objectName: 'Second' }
      ]
    }

    result = CaseTransformer.camel_to_snake(camel_case_hash)

    # Assert that the keys were transformed to snake_case
    assert_equal 'John', result[:first_name]
    assert_equal 'Doe', result[:last_name]
    assert_equal '<EMAIL>', result[:email_address]

    # Assert that nested objects were also transformed
    assert_equal 123, result[:nested_object][:item_id]
    assert_equal 'Test Item', result[:nested_object][:item_name]

    # Assert that arrays of objects were also transformed
    assert_equal 1, result[:array_of_objects][0][:object_id]
    assert_equal 'First', result[:array_of_objects][0][:object_name]
    assert_equal 2, result[:array_of_objects][1][:object_id]
    assert_equal 'Second', result[:array_of_objects][1][:object_name]
  end

  test "snake_to_camel transforms snake_case to camelCase" do
    # Test with a hash
    snake_case_hash = {
      first_name: 'John',
      last_name: 'Doe',
      email_address: '<EMAIL>',
      nested_object: {
        item_id: 123,
        item_name: 'Test Item'
      },
      array_of_objects: [
        { object_id: 1, object_name: 'First' },
        { object_id: 2, object_name: 'Second' }
      ]
    }

    result = CaseTransformer.snake_to_camel(snake_case_hash)

    # Assert that the keys were transformed to camelCase
    assert_equal 'John', result['firstName']
    assert_equal 'Doe', result['lastName']
    assert_equal '<EMAIL>', result['emailAddress']

    # Assert that nested objects were also transformed
    assert_equal 123, result['nestedObject']['itemId']
    assert_equal 'Test Item', result['nestedObject']['itemName']

    # Assert that arrays of objects were also transformed
    assert_equal 1, result['arrayOfObjects'][0]['objectId']
    assert_equal 'First', result['arrayOfObjects'][0]['objectName']
    assert_equal 2, result['arrayOfObjects'][1]['objectId']
    assert_equal 'Second', result['arrayOfObjects'][1]['objectName']
  end

  test "parse_json_to_snake_case parses JSON and transforms to snake_case" do
    # Test with a JSON string
    json_string = {
      firstName: 'John',
      lastName: 'Doe',
      nestedObject: { itemId: 123 }
    }.to_json

    result = CaseTransformer.parse_json_to_snake_case(json_string)

    # Assert that the JSON was parsed and transformed
    assert_equal 'John', result['first_name']
    assert_equal 'Doe', result['last_name']
    assert_equal 123, result['nested_object']['item_id']

    # Test with an array at the root
    json_array = [
      { firstName: 'John' },
      { firstName: 'Jane' }
    ].to_json

    result = CaseTransformer.parse_json_to_snake_case(json_array)

    # Assert that the array is returned directly (not wrapped in _json)
    assert_equal 'John', result[0]['first_name']
    assert_equal 'Jane', result[1]['first_name']

    # Test with empty string
    result = CaseTransformer.parse_json_to_snake_case('')
    assert_equal({}, result)

    # Test with invalid JSON
    result = CaseTransformer.parse_json_to_snake_case('{"key":"value",}')
    assert_equal({}, result)
  end

  test "to_camel_case_json transforms to camelCase and converts to JSON" do
    # Test with a hash
    snake_case_hash = {
      first_name: 'John',
      last_name: 'Doe',
      nested_object: { item_id: 123 }
    }

    json_string = CaseTransformer.to_camel_case_json(snake_case_hash)
    result = JSON.parse(json_string)

    # Assert that the hash was transformed and converted to JSON
    assert_equal 'John', result['firstName']
    assert_equal 'Doe', result['lastName']
    assert_equal 123, result['nestedObject']['itemId']
  end

  test "indifferent access works with parse_json_to_snake_case" do
    # Test with a JSON string
    json_string = { firstName: 'John' }.to_json

    result = CaseTransformer.parse_json_to_snake_case(json_string)

    # Assert that both string and symbol keys work
    assert_equal 'John', result['first_name']
    assert_equal 'John', result[:first_name]
  end

  test "camel_to_snake with symbolize=false returns hash with indifferent access" do
    camel_case_hash = { firstName: 'John' }

    result = CaseTransformer.camel_to_snake(camel_case_hash, symbolize: false)

    # Assert that both string and symbol keys work
    assert_equal 'John', result['first_name']
    assert_equal 'John', result[:first_name]
  end

  test "snake_to_camel with stringify=false doesn't explicitly stringify keys" do
    snake_case_hash = { first_name: 'John' }

    result = CaseTransformer.snake_to_camel(snake_case_hash, stringify: false)

    # Assert that the value is accessible (keys are strings by default due to camelize)
    assert_equal 'John', result['firstName']

    # The stringify parameter only controls whether we explicitly call transform_keys(&:to_s)
    # But the camelize method already returns strings
  end

  test "nil handling in all methods" do
    # Test nil handling in camel_to_snake
    assert_nil CaseTransformer.camel_to_snake(nil)

    # Test nil handling in snake_to_camel
    assert_nil CaseTransformer.snake_to_camel(nil)

    # Test nil handling in to_camel_case_json
    assert_equal '{}', CaseTransformer.to_camel_case_json(nil)
  end
end
