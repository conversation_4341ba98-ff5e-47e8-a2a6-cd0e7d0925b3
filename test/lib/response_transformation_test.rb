# frozen_string_literal: true

require 'test_helper'

# This test ensures that the CamelCaseResponseMiddleware correctly uses the CaseTransformer service
class ResponseTransformationTest < ActiveSupport::TestCase
  test "snake_case keys in responses are transformed to camelCase" do
    # Create a middleware instance
    middleware = CamelCaseResponseMiddleware.new(lambda do |env|
      # Mock a successful JSON response with snake_case keys
      status = 200
      headers = { "Content-Type" => "application/json" }
      body = [{
        id: 1,
        first_name: '<PERSON>',
        last_name: 'Do<PERSON>',
        email_address: '<EMAIL>',
        nested_object: {
          item_id: 123,
          item_name: 'Test Item'
        },
        array_of_objects: [
          { object_id: 1, object_name: 'First' },
          { object_id: 2, object_name: 'Second' }
        ]
      }.to_json]

      [status, headers, body]
    end)

    # Process a mock request through the middleware
    status, headers, response = middleware.call({})

    # Parse the response body
    json_response = JSON.parse(response.first)

    # Assert that the keys were transformed to camelCase
    assert_equal 1, json_response['id'] # ID should remain as is
    assert_equal 'John', json_response['firstName']
    assert_equal 'Doe', json_response['lastName']
    assert_equal '<EMAIL>', json_response['emailAddress']

    # Assert that nested objects were also transformed
    assert_equal 123, json_response['nestedObject']['itemId']
    assert_equal 'Test Item', json_response['nestedObject']['itemName']

    # Assert that arrays of objects were also transformed
    assert_equal 1, json_response['arrayOfObjects'][0]['objectId']
    assert_equal 'First', json_response['arrayOfObjects'][0]['objectName']
    assert_equal 2, json_response['arrayOfObjects'][1]['objectId']
    assert_equal 'Second', json_response['arrayOfObjects'][1]['objectName']

    # Assert that snake_case keys are not present
    assert_nil json_response['first_name']
    assert_nil json_response['last_name']
    assert_nil json_response['email_address']
    assert_nil json_response['nested_object']
    assert_nil json_response['array_of_objects']
  end

  test "Content-Length header is updated correctly" do
    # Create a middleware instance with a Content-Length header
    original_body = [{ first_name: 'John' }.to_json]
    original_length = original_body.first.bytesize.to_s

    middleware = CamelCaseResponseMiddleware.new(lambda do |env|
      status = 200
      headers = { "Content-Type" => "application/json", "Content-Length" => original_length }
      [status, headers, original_body]
    end)

    # Process a mock request through the middleware
    status, headers, response = middleware.call({})

    # The transformed response should have a different length
    transformed_length = response.first.bytesize.to_s

    # Assert that Content-Length was updated
    assert_equal transformed_length, headers["Content-Length"]
    assert_not_equal original_length, transformed_length
  end

  test "non-JSON responses are not transformed" do
    # Create a middleware instance with a non-JSON response
    original_body = ["<html><body>Hello World</body></html>"]

    middleware = CamelCaseResponseMiddleware.new(lambda do |env|
      status = 200
      headers = { "Content-Type" => "text/html" }
      [status, headers, original_body]
    end)

    # Process a mock request through the middleware
    status, headers, response = middleware.call({})

    # Assert that the response was not transformed
    assert_equal original_body, response
  end

  test "invalid JSON responses are handled gracefully" do
    # Create a middleware instance with invalid JSON
    invalid_json = ["{ invalid json }"]

    middleware = CamelCaseResponseMiddleware.new(lambda do |env|
      status = 200
      headers = { "Content-Type" => "application/json" }
      [status, headers, invalid_json]
    end)

    # Process a mock request through the middleware
    status, headers, response = middleware.call({})

    # Assert that the original invalid JSON is returned
    assert_equal invalid_json, response
  end

  # Note: Caching test removed as caching mechanism was removed from the middleware
end
