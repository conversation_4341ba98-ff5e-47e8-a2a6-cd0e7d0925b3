# frozen_string_literal: true

require 'test_helper'

# This test ensures that the parameter transformation initializer correctly uses the CaseTransformer service
class ParameterTransformationTest < ActiveSupport::TestCase
  test "camelCase parameters are transformed to snake_case" do
    # Get the JSON parameter parser
    parser = ActionDispatch::Request.parameter_parsers[:json]

    # Create a JSON string with camelCase keys
    json_data = {
      firstName: 'John',
      lastName: 'Doe',
      emailAddress: '<EMAIL>',
      nestedObject: {
        itemId: 123,
        itemName: 'Test Item'
      },
      arrayOfObjects: [
        { objectId: 1, objectName: 'First' },
        { objectId: 2, objectName: 'Second' }
      ]
    }.to_json

    # Parse the JSON using the parameter parser
    result = parser.call(json_data)

    # Assert that the keys were transformed to snake_case
    assert_equal 'John', result['first_name']
    assert_equal 'Doe', result['last_name']
    assert_equal '<EMAIL>', result['email_address']

    # Assert that nested objects were also transformed
    assert_equal 123, result['nested_object']['item_id']
    assert_equal 'Test Item', result['nested_object']['item_name']

    # Assert that arrays of objects were also transformed
    assert_equal 1, result['array_of_objects'][0]['object_id']
    assert_equal 'First', result['array_of_objects'][0]['object_name']
    assert_equal 2, result['array_of_objects'][1]['object_id']
    assert_equal 'Second', result['array_of_objects'][1]['object_name']

    # Assert that camelCase keys are not present
    assert_nil result['firstName']
    assert_nil result['lastName']
    assert_nil result['emailAddress']
    assert_nil result['nestedObject']
    assert_nil result['arrayOfObjects']
  end

  test "array at root level is handled directly" do
    # Get the JSON parameter parser
    parser = ActionDispatch::Request.parameter_parsers[:json]

    # Create a JSON array
    json_data = [
      { firstName: 'John', lastName: 'Doe' },
      { firstName: 'Jane', lastName: 'Smith' }
    ].to_json

    # Parse the JSON using the parameter parser
    result = parser.call(json_data)

    # Assert that the array is returned directly (not wrapped in _json)
    # This is more intuitive and consistent with how JSON is typically handled
    assert_instance_of Array, result
    assert_equal 2, result.length

    # Assert that the array items are transformed
    assert_equal 'John', result[0]['first_name']
    assert_equal 'Doe', result[0]['last_name']
    assert_equal 'Jane', result[1]['first_name']
    assert_equal 'Smith', result[1]['last_name']
  end

  test "empty or invalid JSON returns empty hash" do
    # Get the JSON parameter parser
    parser = ActionDispatch::Request.parameter_parsers[:json]

    # Test with empty string
    result = parser.call('')
    assert_equal({}, result)

    # Test with invalid JSON
    result = parser.call('{"key":"value",}')
    assert_equal({}, result)
  end

  test "indifferent access allows both string and symbol keys" do
    # Get the JSON parameter parser
    parser = ActionDispatch::Request.parameter_parsers[:json]

    # Create a JSON string with camelCase keys
    json_data = { firstName: 'John' }.to_json

    # Parse the JSON using the parameter parser
    result = parser.call(json_data)

    # Assert that both string and symbol keys work
    assert_equal 'John', result['first_name']
    assert_equal 'John', result[:first_name]
  end
end
