---
description: 
globs: 
alwaysApply: true
---
# Rails Form Object Pattern

Form Objects encapsulate form-related logic into dedicated Ruby classes that include ActiveModel::Model. They handle validation, submission, and persistence coordination separate from controllers and models.

## Core Implementation

```ruby
class ExampleForm
  include ActiveModel::Model
  
  # Define form attributes
  attr_accessor :name, :email
  
  # Add validations
  validates :name, :email, presence: true
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }
  
  # Submission logic
  def save
    return false unless valid?
    
    # Persist data to models
    user = User.create(name: name, email: email)
    # Additional model interactions as needed
    
    true
  rescue ActiveRecord::RecordInvalid => e
    errors.add(:base, e.message)
    false
  end
end
```

## Controller Usage

```ruby
def create
  @form = ExampleForm.new(form_params)
  if @form.save
    redirect_to success_path, notice: 'Success!'
  else
    render :new
  end
end
```

## When to Use
- Forms spanning multiple models
- Complex validation logic
- Custom processing before/after saving
- Bloated controllers

Start simple with controller-based logic, then refactor to Form Objects as complexity increases. 