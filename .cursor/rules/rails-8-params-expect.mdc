---
description: 
globs: 
alwaysApply: true
---
# Rails 8 parameters#expect

When handling parameters in Rails controllers:
Use params.expect(:key) instead of params.require(:key) to fetch required parameters
Use params.expect(key: allowed_attributes) instead of the old permit pattern
This replaces the familiar params.require(:model).permit(:attr1, :attr2) pattern
Examples:
```ruby
# OLD WAY
def user_params
  params.require(:user).permit(:name, :email)
end

# NEW WAY
def user_params
  params.expect(user: [:name, :email])
end

# For single parameters
@record = Model.find(params.expect(:id))

# For nested parameters
params.expect(user: {profile: [:bio, :avatar]})