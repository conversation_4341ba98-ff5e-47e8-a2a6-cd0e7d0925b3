---
description: 
globs: 
alwaysApply: true
---
# Design Principles for Evolving Requirements & Robustness

1.  **Start Simple, Evolve with Requirements:** Begin with the simplest solution that meets the *current* need AND follows best practices. As requirements become more complex, refactor towards more flexible solutions rather than over-engineering initially. Prioritize adapting to *actual* needs over anticipating *all possible* futures, while keeping an eye on possible alternatives to
provide questions and make better choices. If you have doubts ALWAYS ask.
2.  **Separate Concerns for Maintainability & Testability:** Configuration logic should handle *wiring things together*. Complex, application-specific logic belongs in dedicated classes (e.g., Service Objects). This keeps configuration clean, makes the core logic easier to understand, test in isolation, and modify without disturbing unrelated infrastructure code. Avoid bloating models with responsibilities tangential to their core data representation (like request-level validation).
3.  **Naming Matters: Reflect Intent & Abstraction Level:** Choose class and method names that accurately describe their *purpose* and the *level of abstraction* intended. Should the name reflect the current implementation detail or the abstract goal or a specific policy.
Consider how likely the implementation is to change versus the stability of the underlying policy or purpose. Clear naming aids understanding and future refactoring.
4.  **Design for Failure (Fail Closed):** Explicitly handle potential error conditions, especially at boundaries (like middleware processing external input or interacting with infrastructure like databases). Don't assume success. Provide clear logging for different error types (`warn` vs. `error`) to aid debugging.
5.  **Understand the Lifecycle:** Be aware of *when* code executes within your framework (e.g., initialization time vs. request time). This impacts what dependencies (like database connections or loaded models) are safely available. Middleware configuration blocks often defer execution to request time, making them safer for runtime dependencies than top-level initializer code.


