---
description: When talking about rails tests
globs: 
alwaysApply: false
---
**Rule:** When writing or reviewing test code for a Rails application, prioritize the following principles, patterns, and examples:

**I. Core Testing Principles and Patterns**

1.  **Clarity and Readability:**
    * Tests should be easy to read and understand.
    * Use the Arrange, Act, Assert pattern to structure tests.
        * Example:
            ```ruby
            def test_addition
              calculator = Calculator.new # Arrange
              result = calculator.add(2, 3) # Act
              assert_equal 5, result # Assert
            end
            ```
2.  **Effective Assertions:**
    * Choose the most specific assertion to clearly express the test's intent.
    * Use custom messages in assertions to provide context on failures.
        * Example: `assert_equal 5, result, "Addition should return 5"`
3.  **Isolation with Test Doubles:**
    * Employ mocks to verify interactions between objects.
        * Example:
            ```ruby
            mock_provider = Minitest::Mock.new
            mock_provider.expect(:charge, true, [User, Numeric])
            payment = Payment.new(provider: mock_provider)
            payment.charge(User.new, 100)
            assert_mock mock_provider # Verifies that the mock was called as expected
            ```
    * Use stubs to replace method implementations and control behavior, isolating the code under test.
        * Example:
            ```ruby
            StripeProvider.stub :new, mock_provider do
              # Code that uses StripeProvider.new
            end
            ```

**II. Mocks in Detail**

1.  **Purpose of Mocks:**
    * Mocks are for verifying interactions (messages sent to objects), not just replacing slow components.
    * Mocks assert that objects communicate as expected.
2.  **Minitest Implementation:**
    * Use `Minitest::Mock` to create mock objects.
    * Define expectations with `expect`. Specify the method, return value, and arguments.
    * Verify expectations with `assert_mock` or `verify`.
3.  **Defining Mocked Methods:**
    * Use `expect` for methods with defined expectations.
    * Use standard Ruby method definitions for mocks used in multiple tests without specific expectations.
4.  **Mocha Integration (If Applicable):**
    * If using Mocha, use `with` for argument matching and `returns` for return values, for a more concise syntax.
    * Mocha provides methods like `never`, `once`, `at_least_once`, `at_least(number)`, and `at_most(number)` to specify the number of invocations.
    * Example (Mocha):
        ```ruby
        mocked_provider = mock('StripeProvider')
        mocked_provider.expects(:charge).with(user, "300").returns(true).once
        ```

**III. Stubs in Detail**

1.  **Purpose of Stubs:**
    * Stubs replace parts of the application to isolate the code being tested.
    * Stubs enable control over the behavior of dependencies.
2.  **Minitest Implementation:**
    * Use `Object#stub` to replace method implementations. Provide the method name, return value, and a block in which the stub is active.
    * Stubs can replace both class and instance methods.
3.  **Stubbing Multiple Methods:**
    * Nest stub blocks when replacing multiple methods.
4.  **Stubbing Instance Variable Methods:**
    * Use `instance_variable_get` to access an object's instance variable and stub its methods.
        * This is powerful but can create tests coupled to implementation details.
5.  **Mocha Integration (If Applicable):**
    * Mocha allows using `expects` and `stubs` interchangeably, focusing on intent.
    * Mocha offers `raises` and `throws` for stubbing methods that raise exceptions or throw symbols.
    * Mocha enables method chaining with `then` to define different behaviors for subsequent invocations.
    * Example (Mocha):
        ```ruby
        mock_provider.stubs(:transfer).raises(ProviderError, "Insufficient funds")
        mock_provider.stubs(:transfer).returns(:ok).then.returns(:ok).then.raises(ProviderError, "Insufficient funds")
        ```

This revised rule provides more comprehensive guidance on using mocks and stubs, emphasizing their purpose and implementation with Minitest, and how Mocha can be used to provide a different syntax.