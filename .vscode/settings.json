{"ruby.format": "rubocop", "ruby.intellisense": "rubyLocate", "ruby.useLanguageServer": true, "ruby.lint": {"rubocop": true}, "[ruby]": {"editor.formatOnSave": true, "editor.defaultFormatter": "Shopify.ruby-lsp", "editor.semanticHighlighting.enabled": true, "editor.semanticTokenColorCustomizations": {"enabled": true}}, "rubyLsp.enabledFeatures": {"codeActions": true, "diagnostics": true, "documentHighlights": true, "documentLink": true, "documentSymbols": true, "foldingRanges": true, "formatting": true, "hover": true, "inlayHint": true, "onTypeFormatting": true, "selectionRanges": true, "semanticHighlighting": true, "completion": true, "codeLens": true, "definition": true, "workspaceSymbol": true}}